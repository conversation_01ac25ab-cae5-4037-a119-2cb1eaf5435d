import 'package:flutter/material.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/plant_identification_results/plant_identification_results.dart';
import '../presentation/home_dashboard/home_dashboard.dart';
import '../presentation/camera_capture/camera_capture.dart';
import '../presentation/my_plant_collection/my_plant_collection.dart';
import '../presentation/plant_health_diagnosis/plant_health_diagnosis.dart';
import '../presentation/photo_preview/photo_preview.dart';
import '../presentation/profile/profile.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String splashScreen = '/splash-screen';
  static const String plantIdentificationResults =
      '/plant-identification-results';
  static const String homeDashboard = '/home-dashboard';
  static const String cameraCapture = '/camera-capture';
  static const String myPlantCollection = '/my-plant-collection';
  static const String plantHealthDiagnosis = '/plant-health-diagnosis';
  static const String photoPreview = '/photo-preview';
  static const String profile = '/profile';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    splashScreen: (context) => const SplashScreen(),
    plantIdentificationResults: (context) => const PlantIdentificationResults(),
    homeDashboard: (context) => const HomeDashboard(),
    cameraCapture: (context) => const CameraCapture(),
    myPlantCollection: (context) => const MyPlantCollection(),
    plantHealthDiagnosis: (context) => const PlantHealthDiagnosis(),
    photoPreview: (context) {
      final args = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
      return PhotoPreviewScreen(
        imagePath: args['imagePath'] as String,
        source: args['source'] as String,
      );
    },
    profile: (context) => const Profile(),
    // TODO: Add your other routes here
  };
}
