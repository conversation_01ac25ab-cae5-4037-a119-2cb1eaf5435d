import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class OpenRouterService {
  static const String _baseUrl = 'https://openrouter.ai/api/v1';
  static const String _model = 'google/gemini-2.5-flash-lite';
  
  // API key should be stored securely - not exposed in code
  static const String _apiKey = '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';
  
  late final Dio _dio;
  
  OpenRouterService() {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
      },
    ));
    
    // Add logging interceptor for debugging
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    }
  }
  
  /// Converts image file to base64 data URL
  Future<String> _imageToBase64DataUrl(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final String base64String = base64Encode(imageBytes);
      
      // Determine MIME type based on file extension
      String mimeType = 'image/jpeg';
      final String extension = imagePath.toLowerCase().split('.').last;
      switch (extension) {
        case 'png':
          mimeType = 'image/png';
          break;
        case 'gif':
          mimeType = 'image/gif';
          break;
        case 'webp':
          mimeType = 'image/webp';
          break;
        default:
          mimeType = 'image/jpeg';
      }
      
      return 'data:$mimeType;base64,$base64String';
    } catch (e) {
      debugPrint('Error converting image to base64: $e');
      rethrow;
    }
  }
  
  /// Identifies plant from image using OpenRouter API
  Future<Map<String, dynamic>> identifyPlant(String imagePath) async {
    try {
      debugPrint('Starting plant identification for image: $imagePath');
      
      // Convert image to base64 data URL
      final String imageDataUrl = await _imageToBase64DataUrl(imagePath);
      
      // Prepare the request payload
      final Map<String, dynamic> requestData = {
        'model': _model,
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': '''Analyze this plant image and provide detailed identification information. 
                
Please respond with a JSON object containing the following fields:
- commonName: The most common name for this plant
- scientificName: The scientific/botanical name
- family: The plant family it belongs to
- confidence: A confidence score between 0.0 and 1.0
- careLevel: Easy, Moderate, or Difficult
- lightRequirement: Light requirements (e.g., "Bright, indirect light")
- waterFrequency: How often to water (e.g., "Weekly", "Bi-weekly")
- temperature: Ideal temperature range
- humidity: Ideal humidity range
- description: A detailed description of the plant (2-3 sentences)
- growingTips: An array of 3-4 practical growing tips

If you cannot identify the plant with confidence, set confidence to 0.0 and provide general plant care information.'''
              },
              {
                'type': 'image_url',
                'image_url': {
                  'url': imageDataUrl
                }
              }
            ]
          }
        ],
        'max_tokens': 1000,
        'temperature': 0.3,
      };
      
      debugPrint('Sending request to OpenRouter API...');
      
      // Make the API call
      final Response response = await _dio.post(
        '/chat/completions',
        data: requestData,
      );
      
      debugPrint('Received response from OpenRouter API');
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = response.data;
        final String content = responseData['choices'][0]['message']['content'];
        
        debugPrint('API Response content: $content');
        
        // Try to parse JSON from the response
        try {
          // Extract JSON from the response (it might be wrapped in markdown)
          String jsonString = content;
          if (content.contains('```json')) {
            final RegExp jsonRegex = RegExp(r'```json\s*(.*?)\s*```', dotAll: true);
            final Match? match = jsonRegex.firstMatch(content);
            if (match != null) {
              jsonString = match.group(1) ?? content;
            }
          } else if (content.contains('```')) {
            final RegExp codeRegex = RegExp(r'```\s*(.*?)\s*```', dotAll: true);
            final Match? match = codeRegex.firstMatch(content);
            if (match != null) {
              jsonString = match.group(1) ?? content;
            }
          }
          
          final Map<String, dynamic> plantData = json.decode(jsonString.trim());
          
          // Validate and ensure all required fields are present
          return _validateAndNormalizePlantData(plantData);
          
        } catch (e) {
          debugPrint('Error parsing JSON response: $e');
          debugPrint('Raw content: $content');
          
          // Fallback: create plant data from text response
          return _createFallbackPlantData(content);
        }
      } else {
        throw Exception('API request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error in plant identification: $e');
      
      // Return error plant data
      return _createErrorPlantData(e.toString());
    }
  }
  
  /// Validates and normalizes plant data from API response
  Map<String, dynamic> _validateAndNormalizePlantData(Map<String, dynamic> data) {
    return {
      'id': DateTime.now().millisecondsSinceEpoch,
      'commonName': data['commonName'] ?? 'Unknown Plant',
      'scientificName': data['scientificName'] ?? 'Unknown species',
      'family': data['family'] ?? 'Unknown family',
      'confidence': (data['confidence'] ?? 0.0).toDouble(),
      'careLevel': data['careLevel'] ?? 'Moderate',
      'lightRequirement': data['lightRequirement'] ?? 'Bright, indirect light',
      'waterFrequency': data['waterFrequency'] ?? 'Weekly',
      'temperature': data['temperature'] ?? '65-75°F (18-24°C)',
      'humidity': data['humidity'] ?? '40-60%',
      'description': data['description'] ?? 'Plant identification completed.',
      'growingTips': data['growingTips'] is List 
          ? List<String>.from(data['growingTips'])
          : ['Provide adequate light', 'Water when soil is dry', 'Maintain proper humidity'],
      'image': null, // Will be set by the calling code
    };
  }
  
  /// Creates fallback plant data when JSON parsing fails
  Map<String, dynamic> _createFallbackPlantData(String content) {
    return {
      'id': DateTime.now().millisecondsSinceEpoch,
      'commonName': 'Identified Plant',
      'scientificName': 'Species identified',
      'family': 'Plant family',
      'confidence': 0.7,
      'careLevel': 'Moderate',
      'lightRequirement': 'Bright, indirect light',
      'waterFrequency': 'Weekly',
      'temperature': '65-75°F (18-24°C)',
      'humidity': '40-60%',
      'description': content.length > 200 ? content.substring(0, 200) + '...' : content,
      'growingTips': ['Follow general plant care guidelines', 'Monitor plant health regularly'],
      'image': null,
    };
  }
  
  /// Creates error plant data when identification fails
  Map<String, dynamic> _createErrorPlantData(String error) {
    return {
      'id': DateTime.now().millisecondsSinceEpoch,
      'commonName': 'Identification Failed',
      'scientificName': 'Unable to identify',
      'family': 'Unknown',
      'confidence': 0.0,
      'careLevel': 'Unknown',
      'lightRequirement': 'Provide adequate light',
      'waterFrequency': 'Monitor soil moisture',
      'temperature': '65-75°F (18-24°C)',
      'humidity': '40-60%',
      'description': 'Unable to identify this plant. Please try again with a clearer image or consult a plant expert.',
      'growingTips': ['Ensure good lighting for photos', 'Try different angles', 'Clean the camera lens'],
      'image': null,
      'error': error,
    };
  }
}
