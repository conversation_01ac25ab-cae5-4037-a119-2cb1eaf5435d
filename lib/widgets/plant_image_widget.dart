import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class PlantImageWidget extends StatelessWidget {
  final String? imagePath;
  final double width;
  final double height;
  final BoxFit fit;
  final Widget? errorWidget;

  const PlantImageWidget({
    Key? key,
    required this.imagePath,
    this.width = 60,
    this.height = 60,
    this.fit = BoxFit.cover,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (imagePath == null || imagePath!.isEmpty) {
      return _buildErrorWidget();
    }

    // Check if it's a local file path
    if (imagePath!.startsWith('/') || imagePath!.startsWith('file://')) {
      return _buildLocalImage();
    }
    
    // Check if it's a network URL
    if (imagePath!.startsWith('http://') || imagePath!.startsWith('https://')) {
      return _buildNetworkImage();
    }

    // Fallback to error widget
    return _buildErrorWidget();
  }

  Widget _buildLocalImage() {
    return Image.file(
      File(imagePath!),
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Error loading local image: $error');
        return _buildErrorWidget();
      },
    );
  }

  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: imagePath!,
      width: width,
      height: height,
      fit: fit,
      errorWidget: (context, url, error) {
        debugPrint('Error loading network image: $error');
        return _buildErrorWidget();
      },
      placeholder: (context, url) => Container(
        width: width,
        height: height,
        color: Colors.grey[200],
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return errorWidget ??
        Container(
          width: width,
          height: height,
          color: Colors.grey[300],
          child: Icon(
            Icons.image_not_supported,
            size: width * 0.3,
            color: Colors.grey[600],
          ),
        );
  }
}
