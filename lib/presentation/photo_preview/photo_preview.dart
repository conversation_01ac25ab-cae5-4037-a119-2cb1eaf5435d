import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/plant_identification_service.dart';
import '../../widgets/custom_icon_widget.dart';

class PhotoPreviewScreen extends StatefulWidget {
  final String imagePath;
  final String source; // 'camera' or 'gallery'

  const PhotoPreviewScreen({
    super.key,
    required this.imagePath,
    required this.source,
  });

  @override
  State<PhotoPreviewScreen> createState() => _PhotoPreviewScreenState();
}

class _PhotoPreviewScreenState extends State<PhotoPreviewScreen>
    with TickerProviderStateMixin {
  bool _isAnalyzing = false;
  double _analysisProgress = 0.0;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  final PlantIdentificationService _plantService = PlantIdentificationService();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _startAnalysisAnimation() {
    _pulseController.repeat(reverse: true);
    
    // Progress animation
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (!_isAnalyzing || !mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _analysisProgress += 0.05;
        if (_analysisProgress >= 0.9) {
          _analysisProgress = 0.9; // Cap at 90% until API call completes
        }
      });
    });
  }

  Future<void> _analyzePhoto() async {
    if (_isAnalyzing) return;

    try {
      HapticFeedback.mediumImpact();

      setState(() {
        _isAnalyzing = true;
        _analysisProgress = 0.0;
      });

      _startAnalysisAnimation();

      // Call the real plant identification API
      final Map<String, dynamic> plantData = 
          await _plantService.identifyPlantFromImage(widget.imagePath);

      debugPrint('Plant identification completed: ${plantData['commonName']}');

      if (mounted) {
        // Stop animations
        _pulseController.stop();
        
        // Navigate to results
        Navigator.pushReplacementNamed(
          context,
          '/plant-identification-results',
          arguments: {
            'plantData': plantData,
            'imagePath': widget.imagePath,
          },
        );
      }
    } catch (e) {
      debugPrint('Plant analysis error: $e');
      _showErrorSnackBar('Failed to analyze plant. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
          _analysisProgress = 0.0;
        });
        _pulseController.stop();
      }
    }
  }

  void _retakePhoto() {
    HapticFeedback.selectionClick();
    Navigator.pop(context); // Go back to camera
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.lightTheme.colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Photo preview
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: FileImage(File(widget.imagePath)),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Dark overlay when analyzing
          if (_isAnalyzing)
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.7),
              ),
            ),

          // Analysis overlay
          if (_isAnalyzing)
            Center(
              child: Container(
                padding: EdgeInsets.all(6.w),
                margin: EdgeInsets.symmetric(horizontal: 8.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Container(
                            width: 20.w,
                            height: 20.w,
                            decoration: BoxDecoration(
                              color: AppTheme.lightTheme.colorScheme.primary
                                  .withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: CustomIconWidget(
                                iconName: 'search',
                                color: AppTheme.lightTheme.colorScheme.primary,
                                size: 40,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Analyzing Plant...',
                      style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.lightTheme.colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'Our AI is identifying your plant and gathering care information',
                      textAlign: TextAlign.center,
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurface
                            .withValues(alpha: 0.7),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    // Progress bar
                    Container(
                      width: double.infinity,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: _analysisProgress,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppTheme.lightTheme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      '${(_analysisProgress * 100).toInt()}%',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Top controls (when not analyzing)
          if (!_isAnalyzing)
            Positioned(
              top: MediaQuery.of(context).padding.top + 2.h,
              left: 4.w,
              right: 4.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      width: 12.w,
                      height: 12.w,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: CustomIconWidget(
                          iconName: 'arrow_back',
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                  // Source indicator
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: widget.source == 'camera' ? 'camera' : 'photo',
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          widget.source == 'camera' ? 'Camera' : 'Gallery',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // Bottom controls (when not analyzing)
          if (!_isAnalyzing)
            Positioned(
              bottom: MediaQuery.of(context).padding.bottom + 4.h,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Retake button
                    GestureDetector(
                      onTap: _retakePhoto,
                      child: Container(
                        width: 35.w,
                        height: 6.h,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(30),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CustomIconWidget(
                                iconName: 'refresh',
                                color: Colors.white,
                                size: 20,
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                'Retake',
                                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Analyze button
                    GestureDetector(
                      onTap: _analyzePhoto,
                      child: Container(
                        width: 35.w,
                        height: 6.h,
                        decoration: BoxDecoration(
                          color: AppTheme.lightTheme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.lightTheme.colorScheme.primary
                                  .withValues(alpha: 0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CustomIconWidget(
                                iconName: 'search',
                                color: Colors.white,
                                size: 20,
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                'Analyze',
                                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
