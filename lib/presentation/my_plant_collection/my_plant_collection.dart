import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/app_bottom_navigation_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/multi_select_bottom_bar_widget.dart';
import './widgets/plant_grid_widget.dart';
import './widgets/search_bar_widget.dart';

class MyPlantCollection extends StatefulWidget {
  const MyPlantCollection({Key? key}) : super(key: key);

  @override
  State<MyPlantCollection> createState() => _MyPlantCollectionState();
}

class _MyPlantCollectionState extends State<MyPlantCollection> {
  final TextEditingController _searchController = TextEditingController();
  int _currentIndex = 3; // Collection tab index

  // State variables
  List<Map<String, dynamic>> _allPlants = [];
  List<Map<String, dynamic>> _filteredPlants = [];
  Set<int> _selectedPlants = {};
  bool _isMultiSelectMode = false;
  bool _isLoading = true;

  // Filter state
  Map<String, dynamic> _currentFilters = {
    'healthStatus': 'all',
    'wateringNeeds': 'all',
    'plantType': 'all',
    'sortBy': 'name',
  };

  @override
  void initState() {
    super.initState();
    _loadPlantData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadPlantData() {
    // Mock plant collection data
    _allPlants = [
      {
        "id": 1,
        "nickname": "Sunny the Sunflower",
        "scientificName": "Helianthus annuus",
        "commonName": "Sunflower",
        "imageUrl":
            "https://images.unsplash.com/photo-1597848212624-e6f9e1d7e7b4?fm=jpg&q=60&w=400&ixlib=rb-4.0.3",
        "healthPercentage": 92,
        "daysSinceWatered": 2,
        "lastWatered": DateTime.now().subtract(const Duration(days: 2)),
        "plantType": "flowering",
        "careNotes": "Loves direct sunlight and regular watering",
        "dateAdded": DateTime.now().subtract(const Duration(days: 30)),
      },
      {
        "id": 2,
        "nickname": "Jade Beauty",
        "scientificName": "Crassula ovata",
        "commonName": "Jade Plant",
        "imageUrl":
            "https://images.unsplash.com/photo-1509423350716-97f2360af2e4?fm=jpg&q=60&w=400&ixlib=rb-4.0.3",
        "healthPercentage": 78,
        "daysSinceWatered": 5,
        "lastWatered": DateTime.now().subtract(const Duration(days: 5)),
        "plantType": "succulent",
        "careNotes": "Water sparingly, prefers bright indirect light",
        "dateAdded": DateTime.now().subtract(const Duration(days: 45)),
      },
      {
        "id": 3,
        "nickname": "Monstera Mike",
        "scientificName": "Monstera deliciosa",
        "commonName": "Swiss Cheese Plant",
        "imageUrl":
            "https://images.unsplash.com/photo-**********-1564e58b9e4a?fm=jpg&q=60&w=400&ixlib=rb-4.0.3",
        "healthPercentage": 85,
        "daysSinceWatered": 3,
        "lastWatered": DateTime.now().subtract(const Duration(days: 3)),
        "plantType": "indoor",
        "careNotes": "Needs humidity and indirect bright light",
        "dateAdded": DateTime.now().subtract(const Duration(days: 60)),
      },
      {
        "id": 4,
        "nickname": "Rosie",
        "scientificName": "Rosa rubiginosa",
        "commonName": "Sweet Briar Rose",
        "imageUrl":
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?fm=jpg&q=60&w=400&ixlib=rb-4.0.3",
        "healthPercentage": 65,
        "daysSinceWatered": 8,
        "lastWatered": DateTime.now().subtract(const Duration(days: 8)),
        "plantType": "outdoor",
        "careNotes": "Needs regular pruning and morning sunlight",
        "dateAdded": DateTime.now().subtract(const Duration(days: 20)),
      },
      {
        "id": 5,
        "nickname": "Aloe Vera",
        "scientificName": "Aloe barbadensis miller",
        "commonName": "Aloe Vera",
        "imageUrl":
            "https://images.unsplash.com/photo-1509423350716-97f2360af2e4?fm=jpg&q=60&w=400&ixlib=rb-4.0.3",
        "healthPercentage": 45,
        "daysSinceWatered": 12,
        "lastWatered": DateTime.now().subtract(const Duration(days: 12)),
        "plantType": "succulent",
        "careNotes": "Overwatered - reduce watering frequency",
        "dateAdded": DateTime.now().subtract(const Duration(days: 90)),
      },
      {
        "id": 6,
        "nickname": "Fiddle Leaf Fig",
        "scientificName": "Ficus lyrata",
        "commonName": "Fiddle Leaf Fig",
        "imageUrl":
            "https://images.unsplash.com/photo-1586093248292-4e6f8e8c8b8c?fm=jpg&q=60&w=400&ixlib=rb-4.0.3",
        "healthPercentage": 88,
        "daysSinceWatered": 4,
        "lastWatered": DateTime.now().subtract(const Duration(days: 4)),
        "plantType": "indoor",
        "careNotes": "Stable environment, avoid moving frequently",
        "dateAdded": DateTime.now().subtract(const Duration(days: 15)),
      },
    ];

    setState(() {
      _isLoading = false;
      _applyFiltersAndSearch();
    });
  }

  void _onSearchChanged() {
    _applyFiltersAndSearch();
  }

  void _applyFiltersAndSearch() {
    List<Map<String, dynamic>> filtered = List.from(_allPlants);

    // Apply search filter
    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((plant) {
        final nickname = (plant['nickname'] as String).toLowerCase();
        final scientificName =
            (plant['scientificName'] as String).toLowerCase();
        final commonName = (plant['commonName'] as String).toLowerCase();
        return nickname.contains(searchQuery) ||
            scientificName.contains(searchQuery) ||
            commonName.contains(searchQuery);
      }).toList();
    }

    // Apply health status filter
    if (_currentFilters['healthStatus'] != 'all') {
      filtered = filtered.where((plant) {
        final health = plant['healthPercentage'] as num;
        switch (_currentFilters['healthStatus']) {
          case 'healthy':
            return health >= 80;
          case 'needs_attention':
            return health >= 60 && health < 80;
          case 'critical':
            return health < 60;
          default:
            return true;
        }
      }).toList();
    }

    // Apply watering needs filter
    if (_currentFilters['wateringNeeds'] != 'all') {
      filtered = filtered.where((plant) {
        final daysSinceWatered = plant['daysSinceWatered'] as int;
        switch (_currentFilters['wateringNeeds']) {
          case 'needs_water':
            return daysSinceWatered > 7;
          case 'recently_watered':
            return daysSinceWatered <= 2;
          case 'overdue':
            return daysSinceWatered > 10;
          default:
            return true;
        }
      }).toList();
    }

    // Apply plant type filter
    if (_currentFilters['plantType'] != 'all') {
      filtered = filtered.where((plant) {
        return plant['plantType'] == _currentFilters['plantType'];
      }).toList();
    }

    // Apply sorting
    switch (_currentFilters['sortBy']) {
      case 'health':
        filtered.sort((a, b) => (b['healthPercentage'] as num)
            .compareTo(a['healthPercentage'] as num));
        break;
      case 'last_watered':
        filtered.sort((a, b) => (a['daysSinceWatered'] as int)
            .compareTo(b['daysSinceWatered'] as int));
        break;
      case 'date_added':
        filtered.sort((a, b) =>
            (b['dateAdded'] as DateTime).compareTo(a['dateAdded'] as DateTime));
        break;
      default: // name
        filtered.sort((a, b) =>
            (a['nickname'] as String).compareTo(b['nickname'] as String));
        break;
    }

    setState(() {
      _filteredPlants = filtered;
    });
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheetWidget(
        currentFilters: _currentFilters,
        onFiltersChanged: (filters) {
          setState(() {
            _currentFilters = filters;
          });
          _applyFiltersAndSearch();
        },
      ),
    );
  }

  void _onPlantTap(int plantId) {
    if (_isMultiSelectMode) {
      setState(() {
        if (_selectedPlants.contains(plantId)) {
          _selectedPlants.remove(plantId);
          if (_selectedPlants.isEmpty) {
            _isMultiSelectMode = false;
          }
        } else {
          _selectedPlants.add(plantId);
        }
      });
    } else {
      // Start multi-select mode
      setState(() {
        _isMultiSelectMode = true;
        _selectedPlants.add(plantId);
      });
    }
  }

  void _onPlantWater(int plantId) {
    setState(() {
      final plantIndex =
          _allPlants.indexWhere((plant) => plant['id'] == plantId);
      if (plantIndex != -1) {
        _allPlants[plantIndex]['daysSinceWatered'] = 0;
        _allPlants[plantIndex]['lastWatered'] = DateTime.now();
        // Improve health slightly when watered
        final currentHealth = _allPlants[plantIndex]['healthPercentage'] as num;
        _allPlants[plantIndex]['healthPercentage'] =
            (currentHealth + 5).clamp(0, 100);
      }
    });
    _applyFiltersAndSearch();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Plant watered successfully! 💧'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      ),
    );
  }

  void _onPlantEdit(int plantId) {
    // Navigate to plant edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit plant feature coming soon!')),
    );
  }

  void _onPlantRemove(int plantId) {
    setState(() {
      _allPlants.removeWhere((plant) => plant['id'] == plantId);
      _selectedPlants.remove(plantId);
      if (_selectedPlants.isEmpty) {
        _isMultiSelectMode = false;
      }
    });
    _applyFiltersAndSearch();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Plant removed from collection'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            // Implement undo functionality
          },
        ),
      ),
    );
  }

  void _onRefresh() {
    setState(() {
      _isLoading = true;
    });

    // Simulate refresh delay
    Future.delayed(const Duration(seconds: 1), () {
      _loadPlantData();
    });
  }

  void _waterSelectedPlants() {
    for (final plantId in _selectedPlants) {
      _onPlantWater(plantId);
    }

    setState(() {
      _selectedPlants.clear();
      _isMultiSelectMode = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_selectedPlants.length} plants watered! 💧'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      ),
    );
  }

  void _exportSelectedData() {
    // Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export feature coming soon!')),
    );
  }

  void _deleteSelectedPlants() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Remove Plants'),
        content: Text(
            'Are you sure you want to remove ${_selectedPlants.length} plant${_selectedPlants.length != 1 ? 's' : ''} from your collection?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _allPlants.removeWhere(
                    (plant) => _selectedPlants.contains(plant['id']));
                _selectedPlants.clear();
                _isMultiSelectMode = false;
              });
              _applyFiltersAndSearch();
            },
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.lightTheme.colorScheme.error,
            ),
            child: Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _cancelMultiSelect() {
    setState(() {
      _selectedPlants.clear();
      _isMultiSelectMode = false;
    });
  }

  void _navigateToCamera() {
    Navigator.pushNamed(context, '/camera-capture');
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Navigate to different screens based on tab
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/home-dashboard');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/camera-capture');
        break;
      case 2:
        Navigator.pushReplacementNamed(context, '/plant-health-diagnosis');
        break;
      case 3:
        // Current screen - Collection
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('My Plant Collection'),
        backgroundColor: AppTheme.lightTheme.appBarTheme.backgroundColor,
        foregroundColor: AppTheme.lightTheme.appBarTheme.foregroundColor,
        elevation: 0,
        actions: _isMultiSelectMode
            ? [
                TextButton(
                  onPressed: _cancelMultiSelect,
                  child: Text('Cancel'),
                ),
              ]
            : null,
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
            )
          : _allPlants.isEmpty
              ? EmptyStateWidget(onAddPlant: _navigateToCamera)
              : Column(
                  children: [
                    // Search Bar
                    SearchBarWidget(
                      controller: _searchController,
                      onChanged: (value) => _onSearchChanged(),
                      onFilterTap: _showFilterBottomSheet,
                    ),

                    // Plant Grid
                    Expanded(
                      child: _filteredPlants.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomIconWidget(
                                    iconName: 'search_off',
                                    size: 15.w,
                                    color: AppTheme.lightTheme.textTheme
                                            .bodySmall?.color ??
                                        Colors.grey,
                                  ),
                                  SizedBox(height: 2.h),
                                  Text(
                                    'No plants found',
                                    style: AppTheme
                                        .lightTheme.textTheme.titleMedium,
                                  ),
                                  SizedBox(height: 1.h),
                                  Text(
                                    'Try adjusting your search or filters',
                                    style: AppTheme
                                        .lightTheme.textTheme.bodyMedium
                                        ?.copyWith(
                                      color: AppTheme.lightTheme.textTheme
                                          .bodySmall?.color,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : PlantGridWidget(
                              plants: _filteredPlants,
                              selectedPlants: _selectedPlants,
                              isMultiSelectMode: _isMultiSelectMode,
                              onPlantTap: _onPlantTap,
                              onPlantWater: _onPlantWater,
                              onPlantEdit: _onPlantEdit,
                              onPlantRemove: _onPlantRemove,
                              onRefresh: _onRefresh,
                            ),
                    ),
                  ],
                ),
      floatingActionButton: _isMultiSelectMode
          ? null
          : FloatingActionButton(
              onPressed: _navigateToCamera,
              backgroundColor:
                  AppTheme.lightTheme.floatingActionButtonTheme.backgroundColor,
              child: CustomIconWidget(
                iconName: 'add',
                size: 7.w,
                color: AppTheme
                        .lightTheme.floatingActionButtonTheme.foregroundColor ??
                    Colors.white,
              ),
            ),
      bottomNavigationBar: _isMultiSelectMode
          ? MultiSelectBottomBarWidget(
              selectedCount: _selectedPlants.length,
              onWaterSelected: _waterSelectedPlants,
              onExportSelected: _exportSelectedData,
              onDeleteSelected: _deleteSelectedPlants,
              onCancel: _cancelMultiSelect,
            )
          : AppBottomNavigationWidget(
              currentIndex: _currentIndex,
              onTap: _onTabTapped,
            ),
    );
  }
}
