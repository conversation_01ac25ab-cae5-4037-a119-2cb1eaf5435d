import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import './plant_card_widget.dart';

class PlantGridWidget extends StatelessWidget {
  final List<Map<String, dynamic>> plants;
  final Set<int> selectedPlants;
  final bool isMultiSelectMode;
  final Function(int) onPlantTap;
  final Function(int) onPlantWater;
  final Function(int) onPlantEdit;
  final Function(int) onPlantRemove;
  final VoidCallback? onRefresh;

  const PlantGridWidget({
    Key? key,
    required this.plants,
    required this.selectedPlants,
    required this.isMultiSelectMode,
    required this.onPlantTap,
    required this.onPlantWater,
    required this.onPlantEdit,
    required this.onPlantRemove,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final crossAxisCount = isLandscape ? 3 : 2;

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
        // Add haptic feedback
        await Future.delayed(const Duration(milliseconds: 500));
      },
      color: AppTheme.lightTheme.colorScheme.primary,
      child: GridView.builder(
        padding: EdgeInsets.all(4.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 4.w,
          mainAxisSpacing: 3.h,
          childAspectRatio: 0.75,
        ),
        itemCount: plants.length,
        itemBuilder: (context, index) {
          final plant = plants[index];
          final plantId = plant['id'] as int;
          final isSelected = selectedPlants.contains(plantId);

          return GestureDetector(
            onLongPress: () {
              if (!isMultiSelectMode) {
                onPlantTap(plantId); // This will trigger multi-select mode
              }
            },
            child: Dismissible(
              key: Key('plant_$plantId'),
              direction: DismissDirection.horizontal,
              background: _buildSwipeBackground(
                alignment: Alignment.centerLeft,
                color: AppTheme.lightTheme.colorScheme.primary,
                icon: 'water_drop',
                label: 'Water',
              ),
              secondaryBackground: _buildSwipeBackground(
                alignment: Alignment.centerRight,
                color: AppTheme.lightTheme.colorScheme.error,
                icon: 'delete',
                label: 'Remove',
              ),
              confirmDismiss: (direction) async {
                if (direction == DismissDirection.startToEnd) {
                  onPlantWater(plantId);
                  return false; // Don't dismiss
                } else {
                  return await _showDeleteConfirmation(context);
                }
              },
              onDismissed: (direction) {
                if (direction == DismissDirection.endToStart) {
                  onPlantRemove(plantId);
                }
              },
              child: PlantCardWidget(
                plant: plant,
                isSelected: isSelected,
                onTap: () => onPlantTap(plantId),
                onWater: () => onPlantWater(plantId),
                onEdit: () => onPlantEdit(plantId),
                onRemove: () => onPlantRemove(plantId),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSwipeBackground({
    required Alignment alignment,
    required Color color,
    required String icon,
    required String label,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Align(
        alignment: alignment,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconWidget(
                iconName: icon,
                size: 8.w,
                color: Colors.white,
              ),
              SizedBox(height: 1.h),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _showDeleteConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Remove Plant'),
            content: Text(
                'Are you sure you want to remove this plant from your collection?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.lightTheme.colorScheme.error,
                ),
                child: Text('Remove'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
