import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class PlantCardWidget extends StatelessWidget {
  final Map<String, dynamic> plant;
  final VoidCallback? onTap;
  final VoidCallback? onWater;
  final VoidCallback? onEdit;
  final VoidCallback? onRemove;
  final bool isSelected;

  const PlantCardWidget({
    Key? key,
    required this.plant,
    this.onTap,
    this.onWater,
    this.onEdit,
    this.onRemove,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final healthPercentage = (plant['healthPercentage'] as num).toDouble();
    final daysSinceWatered = plant['daysSinceWatered'] as int;
    final needsWater = daysSinceWatered > 7;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: isSelected
              ? Border.all(color: AppTheme.lightTheme.primaryColor, width: 2)
              : null,
          boxShadow: AppTheme.cardShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plant Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                  color: AppTheme.lightTheme.colorScheme.surface,
                ),
                child: ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                  child: CustomImageWidget(
                    imageUrl: plant['imageUrl'] as String,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

            // Plant Details
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(3.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Plant Name
                    Text(
                      plant['nickname'] as String,
                      style:
                          AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 1.h),

                    // Health Progress Bar
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Health',
                              style: AppTheme.lightTheme.textTheme.bodySmall,
                            ),
                            Text(
                              '${healthPercentage.toInt()}%',
                              style: AppTheme.lightTheme.textTheme.bodySmall
                                  ?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: _getHealthColor(healthPercentage),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 0.5.h),
                        Container(
                          height: 0.8.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: AppTheme.lightTheme.colorScheme.outline
                                .withValues(alpha: 0.2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: healthPercentage / 100,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: _getHealthColor(healthPercentage),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 1.h),

                    // Last Watered
                    Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'water_drop',
                          size: 4.w,
                          color: needsWater
                              ? AppTheme.lightTheme.colorScheme.error
                              : AppTheme.lightTheme.colorScheme.primary,
                        ),
                        SizedBox(width: 1.w),
                        Expanded(
                          child: Text(
                            daysSinceWatered == 0
                                ? 'Watered today'
                                : '$daysSinceWatered days ago',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: needsWater
                                  ? AppTheme.lightTheme.colorScheme.error
                                  : AppTheme
                                      .lightTheme.textTheme.bodySmall?.color,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getHealthColor(double percentage) {
    if (percentage >= 80) {
      return AppTheme.lightTheme.colorScheme.primary;
    } else if (percentage >= 60) {
      return const Color(0xFFF59E0B); // Amber
    } else {
      return AppTheme.lightTheme.colorScheme.error;
    }
  }
}
