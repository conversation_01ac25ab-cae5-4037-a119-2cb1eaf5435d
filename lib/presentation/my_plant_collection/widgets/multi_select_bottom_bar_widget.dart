import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MultiSelectBottomBarWidget extends StatelessWidget {
  final int selectedCount;
  final VoidCallback? onWaterSelected;
  final VoidCallback? onExportSelected;
  final VoidCallback? onDeleteSelected;
  final VoidCallback? onCancel;

  const MultiSelectBottomBarWidget({
    Key? key,
    required this.selectedCount,
    this.onWaterSelected,
    this.onExportSelected,
    this.onDeleteSelected,
    this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Row(
            children: [
              // Cancel Button
              TextButton(
                onPressed: onCancel,
                child: Text('Cancel'),
              ),

              SizedBox(width: 4.w),

              // Selected Count
              Expanded(
                child: Text(
                  '$selectedCount plant${selectedCount != 1 ? 's' : ''} selected',
                  style: AppTheme.lightTheme.textTheme.titleMedium,
                ),
              ),

              // Action Buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Water Button
                  IconButton(
                    onPressed: onWaterSelected,
                    icon: CustomIconWidget(
                      iconName: 'water_drop',
                      size: 6.w,
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                    tooltip: 'Water Selected',
                  ),

                  // Export Button
                  IconButton(
                    onPressed: onExportSelected,
                    icon: CustomIconWidget(
                      iconName: 'file_download',
                      size: 6.w,
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                    tooltip: 'Export Data',
                  ),

                  // Delete Button
                  IconButton(
                    onPressed: onDeleteSelected,
                    icon: CustomIconWidget(
                      iconName: 'delete',
                      size: 6.w,
                      color: AppTheme.lightTheme.colorScheme.error,
                    ),
                    tooltip: 'Remove Selected',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
