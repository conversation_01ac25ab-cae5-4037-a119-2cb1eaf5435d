import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class FilterBottomSheetWidget extends StatefulWidget {
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onFiltersChanged;

  const FilterBottomSheetWidget({
    Key? key,
    required this.currentFilters,
    required this.onFiltersChanged,
  }) : super(key: key);

  @override
  State<FilterBottomSheetWidget> createState() =>
      _FilterBottomSheetWidgetState();
}

class _FilterBottomSheetWidgetState extends State<FilterBottomSheetWidget> {
  late Map<String, dynamic> _filters;

  @override
  void initState() {
    super.initState();
    _filters = Map.from(widget.currentFilters);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: EdgeInsets.only(top: 2.h),
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.outline
                  .withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Plants',
                  style: AppTheme.lightTheme.textTheme.titleLarge,
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _filters = {
                        'healthStatus': 'all',
                        'wateringNeeds': 'all',
                        'plantType': 'all',
                        'sortBy': 'name',
                      };
                    });
                  },
                  child: Text('Reset'),
                ),
              ],
            ),
          ),

          Divider(
              color: AppTheme.lightTheme.colorScheme.outline
                  .withValues(alpha: 0.2)),

          // Filter Options
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildFilterSection(
                    'Health Status',
                    'healthStatus',
                    ['all', 'healthy', 'needs_attention', 'critical'],
                    [
                      'All',
                      'Healthy (80%+)',
                      'Needs Attention (60-79%)',
                      'Critical (<60%)'
                    ],
                  ),
                  SizedBox(height: 3.h),
                  _buildFilterSection(
                    'Watering Needs',
                    'wateringNeeds',
                    ['all', 'needs_water', 'recently_watered', 'overdue'],
                    ['All', 'Needs Water', 'Recently Watered', 'Overdue'],
                  ),
                  SizedBox(height: 3.h),
                  _buildFilterSection(
                    'Plant Type',
                    'plantType',
                    ['all', 'indoor', 'outdoor', 'succulent', 'flowering'],
                    ['All', 'Indoor', 'Outdoor', 'Succulent', 'Flowering'],
                  ),
                  SizedBox(height: 3.h),
                  _buildFilterSection(
                    'Sort By',
                    'sortBy',
                    ['name', 'health', 'last_watered', 'date_added'],
                    ['Name', 'Health Status', 'Last Watered', 'Date Added'],
                  ),
                ],
              ),
            ),
          ),

          // Apply Button
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(4.w),
            child: ElevatedButton(
              onPressed: () {
                widget.onFiltersChanged(_filters);
                Navigator.pop(context);
              },
              child: Text('Apply Filters'),
            ),
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildFilterSection(
    String title,
    String filterKey,
    List<String> values,
    List<String> labels,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.lightTheme.textTheme.titleMedium,
        ),
        SizedBox(height: 1.h),
        Wrap(
          spacing: 2.w,
          runSpacing: 1.h,
          children: values.asMap().entries.map((entry) {
            final index = entry.key;
            final value = entry.value;
            final label = labels[index];
            final isSelected = _filters[filterKey] == value;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _filters[filterKey] = value;
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme.lightTheme.colorScheme.primary
                      : AppTheme.lightTheme.colorScheme.surface,
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.lightTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.outline
                            .withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  label,
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? AppTheme.lightTheme.colorScheme.onPrimary
                        : AppTheme.lightTheme.textTheme.bodyMedium?.color,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
