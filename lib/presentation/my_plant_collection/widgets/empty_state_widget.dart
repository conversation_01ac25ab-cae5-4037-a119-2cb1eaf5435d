import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EmptyStateWidget extends StatelessWidget {
  final VoidCallback? onAddPlant;

  const EmptyStateWidget({
    Key? key,
    this.onAddPlant,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Botanical Illustration
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primary
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20.w),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: 'local_florist',
                  size: 20.w,
                  color: AppTheme.lightTheme.colorScheme.primary
                      .withValues(alpha: 0.6),
                ),
              ),
            ),

            SizedBox(height: 4.h),

            // Title
            Text(
              'No Plants Yet',
              style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.lightTheme.textTheme.titleLarge?.color,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 2.h),

            // Description
            Text(
              'Start building your plant collection by identifying your first plant. Use the camera to capture and learn about plants around you.',
              style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                color: AppTheme.lightTheme.textTheme.bodySmall?.color,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 4.h),

            // CTA Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onAddPlant,
                icon: CustomIconWidget(
                  iconName: 'camera_alt',
                  size: 5.w,
                  color: AppTheme.lightTheme.colorScheme.onPrimary,
                ),
                label: Text('Identify Your First Plant'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                ),
              ),
            ),

            SizedBox(height: 2.h),

            // Secondary Action
            TextButton.icon(
              onPressed: () {
                // Navigate to manual plant addition
              },
              icon: CustomIconWidget(
                iconName: 'add',
                size: 5.w,
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
              label: Text('Add Plant Manually'),
            ),
          ],
        ),
      ),
    );
  }
}
