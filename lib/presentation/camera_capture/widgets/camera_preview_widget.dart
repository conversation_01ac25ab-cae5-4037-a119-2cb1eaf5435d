import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class CameraPreviewWidget extends StatelessWidget {
  final CameraController? cameraController;
  final bool isInitialized;
  final VoidCallback onTapToFocus;

  const CameraPreviewWidget({
    super.key,
    required this.cameraController,
    required this.isInitialized,
    required this.onTapToFocus,
  });

  @override
  Widget build(BuildContext context) {
    if (!isInitialized || cameraController == null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.8),
              Colors.black.withValues(alpha: 0.6),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: AppTheme.lightTheme.colorScheme.primary,
                strokeWidth: 3.0,
              ),
              SizedBox(height: 2.h),
              Text(
                'Starting Camera...',
                style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1.h),
              Text(
                'Please allow camera access if prompted',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withValues(alpha: 0.8),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return GestureDetector(
      onTapUp: (details) => onTapToFocus(),
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(0),
          child: FittedBox(
            fit: BoxFit.cover,
            child: SizedBox(
              width: cameraController!.value.previewSize?.height ?? 1,
              height: cameraController!.value.previewSize?.width ?? 1,
              child: CameraPreview(cameraController!),
            ),
          ),
        ),
      ),
    );
  }
}
