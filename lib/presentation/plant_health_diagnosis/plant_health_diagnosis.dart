import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/app_bottom_navigation_widget.dart';
import './widgets/custom_symptom_input.dart';
import './widgets/diagnosis_results.dart';
import './widgets/loading_analysis.dart';
import './widgets/photo_upload_section.dart';
import './widgets/symptom_checklist.dart';

class PlantHealthDiagnosis extends StatefulWidget {
  const PlantHealthDiagnosis({Key? key}) : super(key: key);

  @override
  State<PlantHealthDiagnosis> createState() => _PlantHealthDiagnosisState();
}

class _PlantHealthDiagnosisState extends State<PlantHealthDiagnosis> {
  int _currentIndex = 2; // Diagnosis tab index
  XFile? _selectedImage;
  List<String> _selectedSymptoms = [];
  String _customSymptom = '';
  bool _isAnalyzing = false;
  Map<String, dynamic>? _diagnosisResults;

  // Mock diagnosis data for demonstration
  final List<Map<String, dynamic>> _mockDiagnosisData = [
    {
      'confidence': 87.5,
      'severity': 'Medium',
      'problem': 'Root Rot (Pythium)',
      'treatments': [
        'Remove plant from pot and inspect roots carefully',
        'Cut away all black, mushy, or damaged roots with sterile scissors',
        'Rinse remaining healthy roots with clean water',
        'Repot in fresh, well-draining potting mix',
        'Reduce watering frequency and ensure proper drainage',
        'Apply fungicide treatment if available',
      ],
      'timeline': '2-4 weeks with proper care',
      'followUp': [
        'Monitor soil moisture daily',
        'Check for new growth after 2 weeks',
        'Adjust watering schedule based on soil dryness',
        'Watch for yellowing leaves as sign of continued stress',
      ],
    },
    {
      'confidence': 92.3,
      'severity': 'Low',
      'problem': 'Nutrient Deficiency (Nitrogen)',
      'treatments': [
        'Apply balanced liquid fertilizer diluted to half strength',
        'Increase feeding frequency to every 2 weeks during growing season',
        'Ensure adequate light for nutrient uptake',
        'Check soil pH - should be between 6.0-7.0 for most plants',
        'Consider repotting if soil is old or depleted',
      ],
      'timeline': '3-6 weeks for visible improvement',
      'followUp': [
        'Monitor new leaf color and growth',
        'Reduce fertilizer in winter months',
        'Test soil pH if problems persist',
      ],
    },
    {
      'confidence': 78.9,
      'severity': 'High',
      'problem': 'Spider Mite Infestation',
      'treatments': [
        'Isolate plant immediately to prevent spread',
        'Spray leaves with strong water stream to remove mites',
        'Apply insecticidal soap or neem oil every 3 days',
        'Increase humidity around plant (mites prefer dry conditions)',
        'Remove heavily infested leaves',
        'Clean surrounding area thoroughly',
      ],
      'timeline': '1-2 weeks with consistent treatment',
      'followUp': [
        'Continue treatment for 2 weeks after last mite seen',
        'Monitor weekly for re-infestation',
        'Maintain higher humidity levels',
        'Quarantine new plants before introducing to collection',
      ],
    },
  ];

  void _onImageSelected(XFile? image) {
    setState(() {
      _selectedImage = image;
    });
  }

  void _onSymptomsChanged(List<String> symptoms) {
    setState(() {
      _selectedSymptoms = symptoms;
    });
  }

  void _onCustomSymptomChanged(String symptom) {
    setState(() {
      _customSymptom = symptom;
    });
  }

  Future<void> _startAnalysis() async {
    if (_selectedImage == null &&
        _selectedSymptoms.isEmpty &&
        _customSymptom.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please upload a photo or select symptoms to analyze'),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
        ),
      );
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _diagnosisResults = null;
    });

    // Simulate AI analysis delay
    await Future.delayed(Duration(seconds: 4));

    // Select mock diagnosis based on symptoms or random
    Map<String, dynamic> selectedDiagnosis;
    if (_selectedSymptoms.contains('mushy_stem') ||
        _customSymptom.toLowerCase().contains('root')) {
      selectedDiagnosis = _mockDiagnosisData[0]; // Root rot
    } else if (_selectedSymptoms.contains('yellowing_leaves')) {
      selectedDiagnosis = _mockDiagnosisData[1]; // Nutrient deficiency
    } else if (_selectedSymptoms.contains('pests') ||
        _selectedSymptoms.contains('brown_spots')) {
      selectedDiagnosis = _mockDiagnosisData[2]; // Spider mites
    } else {
      selectedDiagnosis = _mockDiagnosisData[0]; // Default
    }

    if (mounted) {
      setState(() {
        _isAnalyzing = false;
        _diagnosisResults = selectedDiagnosis;
      });
    }
  }

  void _resetDiagnosis() {
    setState(() {
      _selectedImage = null;
      _selectedSymptoms.clear();
      _customSymptom = '';
      _diagnosisResults = null;
      _isAnalyzing = false;
    });
  }

  void _saveToProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Diagnosis saved to your plant profile'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      ),
    );
  }

  void _shareResults() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing diagnosis results...'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Navigate to different screens based on tab
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/home-dashboard');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/camera-capture');
        break;
      case 2:
        // Current screen - Diagnosis
        break;
      case 3:
        Navigator.pushReplacementNamed(context, '/my-plant-collection');
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Plant Health Diagnosis',
          style: AppTheme.lightTheme.appBarTheme.titleTextStyle,
        ),
        backgroundColor: AppTheme.lightTheme.appBarTheme.backgroundColor,
        elevation: AppTheme.lightTheme.appBarTheme.elevation,
        actions: [
          if (_diagnosisResults != null)
            IconButton(
              onPressed: _resetDiagnosis,
              icon: CustomIconWidget(
                iconName: 'refresh',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              tooltip: 'New Diagnosis',
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.lightTheme.colorScheme.primary
                            .withValues(alpha: 0.1),
                        AppTheme.lightTheme.colorScheme.secondary
                            .withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      CustomIconWidget(
                        iconName: 'medical_services',
                        color: AppTheme.lightTheme.colorScheme.primary,
                        size: 48,
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        'AI Plant Doctor',
                        style: AppTheme.lightTheme.textTheme.headlineSmall
                            ?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        'Upload a photo and describe symptoms to get instant diagnosis and treatment recommendations',
                        style:
                            AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color:
                              AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 3.h),
              ],

              // Photo Upload Section
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                PhotoUploadSection(
                  onImageSelected: _onImageSelected,
                ),
                SizedBox(height: 3.h),
              ],

              // Symptom Checklist
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                SymptomChecklist(
                  onSymptomsChanged: _onSymptomsChanged,
                ),
                SizedBox(height: 3.h),
              ],

              // Custom Symptom Input
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                CustomSymptomInput(
                  onCustomSymptomChanged: _onCustomSymptomChanged,
                ),
                SizedBox(height: 4.h),
              ],

              // Analyze Button
              if (_diagnosisResults == null && !_isAnalyzing) ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _startAnalysis,
                    icon: CustomIconWidget(
                      iconName: 'psychology',
                      color: AppTheme.lightTheme.colorScheme.onPrimary,
                      size: 20,
                    ),
                    label: Text('Analyze Plant Health'),
                    style:
                        AppTheme.lightTheme.elevatedButtonTheme.style?.copyWith(
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                      ),
                    ),
                  ),
                ),
              ],

              // Loading Analysis
              if (_isAnalyzing) ...[
                LoadingAnalysis(),
              ],

              // Diagnosis Results
              if (_diagnosisResults != null) ...[
                DiagnosisResults(
                  diagnosisData: _diagnosisResults!,
                  onSaveToProfile: _saveToProfile,
                  onShareResults: _shareResults,
                ),
              ],
            ],
          ),
        ),
      ),
      bottomNavigationBar: AppBottomNavigationWidget(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }
}
