import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/app_bottom_navigation_widget.dart';
import './widgets/action_buttons_widget.dart';
import './widgets/alternative_suggestions_widget.dart';
import './widgets/care_requirements_widget.dart';
import './widgets/plant_description_widget.dart';
import './widgets/plant_image_header_widget.dart';
import './widgets/plant_info_card_widget.dart';

class PlantIdentificationResults extends StatefulWidget {
  const PlantIdentificationResults({Key? key}) : super(key: key);

  @override
  State<PlantIdentificationResults> createState() =>
      _PlantIdentificationResultsState();
}

class _PlantIdentificationResultsState extends State<PlantIdentificationResults>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  int _currentIndex = 3; // Camera tab index (since we came from camera)

  // Plant data will be received from navigation arguments
  Map<String, dynamic>? plantData;
  String? imagePath;

  final List<Map<String, dynamic>> alternativePlants = [
    {
      "name": "Philodendron Bipinnatifidum",
      "confidence": 78.3,
      "image":
          "https://images.unsplash.com/photo-1586093248292-4e6f6d4e1e4b?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3"
    },
    {
      "name": "Monstera Adansonii",
      "confidence": 65.7,
      "image":
          "https://images.unsplash.com/photo-1592150621744-aca64f48394a?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3"
    },
    {
      "name": "Epipremnum Aureum",
      "confidence": 52.1,
      "image":
          "https://images.unsplash.com/photo-1586093248292-4e6f6d4e1e4b?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3"
    }
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Extract plant data from navigation arguments
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      plantData = arguments['plantData'] as Map<String, dynamic>?;
      imagePath = arguments['imagePath'] as String?;
    }
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    _animationController.forward();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Navigate to different screens based on tab
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/splash-screen');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/home-dashboard');
        break;
      case 2:
        Navigator.pushReplacementNamed(context, '/plant-health-diagnosis');
        break;
      case 3:
        Navigator.pushReplacementNamed(context, '/camera-capture');
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/my-plant-collection');
        break;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show loading or error if plant data is not available
    if (plantData == null) {
      return Scaffold(
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: Column(
        children: [
          // Plant image header with glassmorphism overlay
          PlantImageHeaderWidget(
            imageUrl: imagePath ?? plantData!["image"] as String? ?? "",
            confidence: (plantData!["confidence"] as num?)?.toDouble() ?? 0.0,
          ),

          // Scrollable content
          Expanded(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        children: [
                          SizedBox(height: 2.h),

                          // Plant identification card
                          PlantInfoCardWidget(
                            commonName: plantData!["commonName"] as String? ?? "Unknown Plant",
                            scientificName:
                                plantData!["scientificName"] as String? ?? "Unknown species",
                            family: plantData!["family"] as String? ?? "Unknown family",
                          ),

                          SizedBox(height: 3.h),

                          // Care requirements with progress indicators
                          CareRequirementsWidget(
                            lightRequirement:
                                plantData!["lightRequirement"] as String? ?? "Unknown",
                            wateringFrequency:
                                plantData!["wateringFrequency"] as String? ?? plantData!["waterFrequency"] as String? ?? "Unknown",
                            temperatureRange:
                                plantData!["temperatureRange"] as String? ?? plantData!["temperature"] as String? ?? "Unknown",
                            lightLevel: (plantData!["lightLevel"] as num?)?.toDouble() ?? 0.5,
                            waterLevel: (plantData!["waterLevel"] as num?)?.toDouble() ?? 0.5,
                            temperatureLevel:
                                (plantData!["temperatureLevel"] as num?)?.toDouble() ?? 0.5,
                          ),

                          SizedBox(height: 3.h),

                          // Plant description and growing tips
                          PlantDescriptionWidget(
                            description: plantData!["description"] as String? ?? "No description available.",
                            growingTips: (plantData!["growingTips"] as List?)?.cast<String>() ?? ["No growing tips available."],
                          ),

                          SizedBox(height: 3.h),

                          // Alternative identification suggestions
                          AlternativeSuggestionsWidget(
                            alternatives: alternativePlants,
                          ),

                          SizedBox(
                              height: 20
                                  .h), // Space for bottom actions and navigation
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),

      // Bottom action buttons and navigation
      bottomSheet: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ActionButtonsWidget(
            plantData: plantData ?? {},
            onAddToCollection: _addToCollection,
          ),
          AppBottomNavigationWidget(
            currentIndex: _currentIndex,
            onTap: _onTabTapped,
          ),
        ],
      ),
    );
  }

  void _addToCollection() {
    // Show success dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              CustomIconWidget(
                iconName: 'check_circle',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              SizedBox(width: 2.w),
              Text(
                'Added to Collection',
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: Text(
            '${plantData?["commonName"] ?? "Plant"} has been successfully added to your plant collection. You can now track its health and care schedule.',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Continue',
                style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/my-plant-collection');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.lightTheme.colorScheme.primary,
                foregroundColor: AppTheme.lightTheme.colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'View Collection',
                style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
