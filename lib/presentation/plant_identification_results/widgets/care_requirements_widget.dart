import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CareRequirementsWidget extends StatelessWidget {
  final String lightRequirement;
  final String wateringFrequency;
  final String temperatureRange;
  final double lightLevel;
  final double waterLevel;
  final double temperatureLevel;

  const CareRequirementsWidget({
    Key? key,
    required this.lightRequirement,
    required this.wateringFrequency,
    required this.temperatureRange,
    required this.lightLevel,
    required this.waterLevel,
    required this.temperatureLevel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.cardShadow,
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Care Requirements',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),

          SizedBox(height: 3.h),

          // Light requirements
          _buildCareItem(
            icon: 'wb_sunny',
            title: 'Light',
            description: lightRequirement,
            level: lightLevel,
            color: Colors.amber,
          ),

          SizedBox(height: 2.h),

          // Water requirements
          _buildCareItem(
            icon: 'water_drop',
            title: 'Water',
            description: wateringFrequency,
            level: waterLevel,
            color: Colors.blue,
          ),

          SizedBox(height: 2.h),

          // Temperature requirements
          _buildCareItem(
            icon: 'thermostat',
            title: 'Temperature',
            description: temperatureRange,
            level: temperatureLevel,
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildCareItem({
    required String icon,
    required String title,
    required String description,
    required double level,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 10.w,
              height: 5.h,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: icon,
                  color: color,
                  size: 20,
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    description,
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: 1.h),

        // Progress indicator
        Container(
          width: double.infinity,
          height: 0.8.h,
          decoration: BoxDecoration(
            color:
                AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: level / 100,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    color.withValues(alpha: 0.7),
                    color,
                  ],
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
