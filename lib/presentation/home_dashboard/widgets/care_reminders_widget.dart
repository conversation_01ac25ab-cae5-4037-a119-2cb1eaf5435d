import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CareRemindersWidget extends StatefulWidget {
  final List<Map<String, dynamic>> reminders;
  final Function(Map<String, dynamic>) onCompleteTask;

  const CareRemindersWidget({
    Key? key,
    required this.reminders,
    required this.onCompleteTask,
  }) : super(key: key);

  @override
  State<CareRemindersWidget> createState() => _CareRemindersWidgetState();
}

class _CareRemindersWidgetState extends State<CareRemindersWidget> {
  final List<int> completedTasks = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          child: Text(
            'Today\'s Care Reminders',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
        ),
        SizedBox(height: 1.h),
        widget.reminders.isEmpty ? _buildEmptyState() : _buildRemindersList(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(4.w),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'check_circle',
            color: AppTheme.lightTheme.colorScheme.primary,
            size: 12.w,
          ),
          SizedBox(height: 2.h),
          Text(
            'All caught up!',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'No care tasks for today',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersList() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(4.w),
        boxShadow: AppTheme.cardShadow,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.all(3.w),
        itemCount: widget.reminders.length,
        separatorBuilder: (context, index) => Divider(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
          height: 2.h,
        ),
        itemBuilder: (context, index) {
          final reminder = widget.reminders[index];
          final isCompleted = completedTasks.contains(reminder['id'] as int);
          return _buildReminderItem(reminder, isCompleted, index);
        },
      ),
    );
  }

  Widget _buildReminderItem(
      Map<String, dynamic> reminder, bool isCompleted, int index) {
    final taskType = reminder['taskType'] as String;
    final plantName = reminder['plantName'] as String;
    final priority = reminder['priority'] as String;

    final priorityColor = priority == 'high'
        ? AppTheme.lightTheme.colorScheme.error
        : priority == 'medium'
            ? Colors.orange
            : AppTheme.lightTheme.colorScheme.primary;

    final taskIcon = taskType == 'water'
        ? 'water_drop'
        : taskType == 'fertilize'
            ? 'eco'
            : taskType == 'prune'
                ? 'content_cut'
                : 'local_florist';

    return Dismissible(
      key: Key('reminder_${reminder['id']}'),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 4.w),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.primary,
          borderRadius: BorderRadius.circular(2.w),
        ),
        child: CustomIconWidget(
          iconName: 'check',
          color: Colors.white,
          size: 6.w,
        ),
      ),
      onDismissed: (direction) {
        setState(() {
          completedTasks.add(reminder['id'] as int);
        });
        widget.onCompleteTask(reminder);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Task completed for $plantName'),
            duration: const Duration(seconds: 2),
          ),
        );
      },
      child: AnimatedOpacity(
        opacity: isCompleted ? 0.5 : 1.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: isCompleted
                ? AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(2.w),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: priorityColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(2.w),
                ),
                child: CustomIconWidget(
                  iconName: taskIcon,
                  color: priorityColor,
                  size: 5.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${taskType.substring(0, 1).toUpperCase()}${taskType.substring(1)} $plantName',
                      style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.lightTheme.colorScheme.onSurface,
                        decoration:
                            isCompleted ? TextDecoration.lineThrough : null,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 0.5.h),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 2.w, vertical: 0.5.h),
                          decoration: BoxDecoration(
                            color: priorityColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(1.w),
                          ),
                          child: Text(
                            '${priority.toUpperCase()} PRIORITY',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: priorityColor,
                              fontWeight: FontWeight.w600,
                              fontSize: 10.sp,
                            ),
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          reminder['time'] as String,
                          style:
                              AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme
                                .lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              isCompleted
                  ? CustomIconWidget(
                      iconName: 'check_circle',
                      color: AppTheme.lightTheme.colorScheme.primary,
                      size: 6.w,
                    )
                  : CustomIconWidget(
                      iconName: 'chevron_right',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 5.w,
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
