import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/animated_logo_widget.dart';
import './widgets/app_title_widget.dart';
import './widgets/background_gradient_widget.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _isInitializing = true;
  String _initializationStatus = 'Initializing AI Plant Recognition...';

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Set system UI overlay style for immersive experience
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
      );

      // Simulate initialization tasks with realistic timing
      await _performInitializationTasks();

      // Navigate to home dashboard after successful initialization
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/home-dashboard');
      }
    } catch (e) {
      // Handle initialization errors gracefully
      if (mounted) {
        _showRetryDialog();
      }
    }
  }

  Future<void> _performInitializationTasks() async {
    final List<Map<String, dynamic>> initTasks = [
      {
        'message': 'Checking camera permissions...',
        'duration': 500,
      },
      {
        'message': 'Loading plant database cache...',
        'duration': 800,
      },
      {
        'message': 'Initializing AI model...',
        'duration': 1000,
      },
      {
        'message': 'Preparing offline plant collection...',
        'duration': 600,
      },
      {
        'message': 'Ready to identify plants!',
        'duration': 300,
      },
    ];

    for (final task in initTasks) {
      if (mounted) {
        setState(() {
          _initializationStatus = task['message'] as String;
        });
        await Future.delayed(Duration(milliseconds: task['duration'] as int));
      }
    }

    setState(() {
      _isInitializing = false;
    });

    // Additional delay for smooth transition
    await Future.delayed(const Duration(milliseconds: 500));
  }

  void _showRetryDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppTheme.lightTheme.dialogBackgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          title: Text(
            'Initialization Failed',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          content: Text(
            'Unable to initialize the app. Please check your internet connection and try again.',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _initializeApp();
              },
              child: Text(
                'Retry',
                style: TextStyle(
                  color: AppTheme.lightTheme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background gradient with botanical elements
          const BackgroundGradientWidget(),

          // Main content
          SafeArea(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Spacer to push content slightly up
                  const Spacer(flex: 2),

                  // Animated logo
                  const AnimatedLogoWidget(),

                  SizedBox(height: 4.h),

                  // App title with animation
                  const AppTitleWidget(),

                  const Spacer(flex: 3),

                  // Loading indicator with status
                  _isInitializing
                      ? Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 8.w,
                              height: 8.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 3.0,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white.withValues(alpha: 0.8),
                                ),
                                backgroundColor:
                                    Colors.white.withValues(alpha: 0.2),
                              ),
                            ),
                            SizedBox(height: 2.h),
                            Container(
                              constraints: BoxConstraints(maxWidth: 80.w),
                              child: Text(
                                _initializationStatus,
                                style: AppTheme.lightTheme.textTheme.bodyMedium
                                    ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontWeight: FontWeight.w400,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        )
                      : Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6.w,
                            vertical: 1.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CustomIconWidget(
                                iconName: 'check_circle',
                                color: Colors.white,
                                size: 5.w,
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                'Ready!',
                                style: AppTheme.lightTheme.textTheme.bodyMedium
                                    ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),

                  SizedBox(height: 8.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
