import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class BackgroundGradientWidget extends StatefulWidget {
  const BackgroundGradientWidget({super.key});

  @override
  State<BackgroundGradientWidget> createState() =>
      _BackgroundGradientWidgetState();
}

class _BackgroundGradientWidgetState extends State<BackgroundGradientWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _gradientAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _gradientAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.lerp(
                  AppTheme.lightTheme.colorScheme.primary,
                  AppTheme.lightTheme.colorScheme.secondary,
                  _gradientAnimation.value * 0.3,
                )!,
                Color.lerp(
                  AppTheme.lightTheme.colorScheme.secondary,
                  AppTheme.lightTheme.colorScheme.primary,
                  _gradientAnimation.value * 0.3,
                )!,
              ],
              stops: const [0.0, 1.0],
            ),
          ),
          child: Stack(
            children: [
              // Subtle botanical pattern overlay
              Positioned(
                top: -10.h,
                right: -15.w,
                child: Opacity(
                  opacity: 0.1,
                  child: Transform.rotate(
                    angle: 0.2,
                    child: CustomIconWidget(
                      iconName: 'local_florist',
                      color: Colors.white,
                      size: 40.w,
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: -8.h,
                left: -12.w,
                child: Opacity(
                  opacity: 0.08,
                  child: Transform.rotate(
                    angle: -0.3,
                    child: CustomIconWidget(
                      iconName: 'nature',
                      color: Colors.white,
                      size: 35.w,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
